// fix_orderbook_normalization.rs
// Fix the normalization issues in existing orderbook parquet files

use polars::prelude::*;
use std::path::Path;

/// Fix normalization in existing orderbook parquet files
/// This addresses the extreme outlier problem in quantity features
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let input_files = vec![
        "/mnt/storage-box/bybit/orderbooks/output/2025-04-30_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-01_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-02_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-03_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-04_BTCUSDC_financialbert.parquet",
    ];

    for input_file in input_files {
        if !Path::new(input_file).exists() {
            println!("⚠️  Skipping non-existent file: {}", input_file);
            continue;
        }

        println!("🔧 Fixing normalization for: {}", input_file);
        fix_file_normalization(input_file)?;
    }

    println!("✅ All files processed successfully!");
    Ok(())
}

fn fix_file_normalization(file_path: &str) -> Result<(), Box<dyn std::error::Error>> {
    // Load the data
    println!("  Loading data...");
    let mut df = LazyFrame::scan_parquet(file_path, Default::default())?
        .collect()?;

    println!("  Original shape: {:?}", df.shape());

    // Identify quantity columns (odd-numbered features after timestamp)
    let column_names = df.get_column_names();
    let mut qty_columns = Vec::new();
    let mut price_columns = Vec::new();

    for col_name in &column_names {
        if col_name.contains("_qty_") {
            qty_columns.push(col_name.to_string());
        } else if col_name.contains("_price_") {
            price_columns.push(col_name.to_string());
        }
    }

    println!("  Found {} price columns and {} quantity columns", 
             price_columns.len(), qty_columns.len());

    // Collect all quantity values for robust statistics
    let mut all_quantities = Vec::new();
    for col_name in &qty_columns {
        let column = df.column(col_name)?;
        let values: Vec<f32> = column
            .f32()?
            .into_iter()
            .filter_map(|opt_val| opt_val)
            .collect();
        all_quantities.extend(values);
    }

    // Calculate robust normalization parameters for quantities
    all_quantities.sort_by(|a, b| a.partial_cmp(b).unwrap());
    let qty_p5 = all_quantities[(all_quantities.len() as f64 * 0.05) as usize];
    let qty_p95 = all_quantities[(all_quantities.len() as f64 * 0.95) as usize];
    let qty_median = all_quantities[all_quantities.len() / 2];
    let qty_iqr = qty_p95 - qty_p5;
    let qty_scale = if qty_iqr > 0.0 { qty_iqr } else { 1.0 };

    println!("  Quantity stats: median={:.4}, IQR={:.4}, P5={:.4}, P95={:.4}", 
             qty_median, qty_iqr, qty_p5, qty_p95);

    // Apply robust normalization to quantity columns
    for col_name in &qty_columns {
        println!("    Fixing column: {}", col_name);
        
        let normalized_col = df.column(col_name)?
            .f32()?
            .into_iter()
            .map(|opt_val| {
                match opt_val {
                    Some(val) => {
                        // Apply robust normalization with clipping
                        let normalized = (val - qty_median) / qty_scale;
                        Some(normalized.clamp(-5.0, 5.0))
                    }
                    None => None
                }
            })
            .collect::<Float32Chunked>()
            .with_name(col_name.into());

        // Replace the column
        df.replace(col_name, normalized_col.into_series())?;
    }

    // Check the results
    println!("  Verifying normalization...");
    for col_name in qty_columns.iter().take(3) { // Check first 3 quantity columns
        let column = df.column(col_name)?;
        let values: Vec<f32> = column
            .f32()?
            .into_iter()
            .filter_map(|opt_val| opt_val)
            .collect();
        
        if !values.is_empty() {
            let mean = values.iter().sum::<f32>() / values.len() as f32;
            let max_val = values.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
            let min_val = values.iter().fold(f32::INFINITY, |a, &b| a.min(b));
            
            println!("    {}: mean={:.4}, min={:.4}, max={:.4}", 
                     col_name, mean, min_val, max_val);
        }
    }

    // Save the fixed file
    let output_path = file_path.replace(".parquet", "_fixed.parquet");
    println!("  Saving to: {}", output_path);
    
    let mut file = std::fs::File::create(&output_path)?;
    ParquetWriter::new(&mut file)
        .with_compression(ParquetCompression::Snappy)
        .finish(&mut df)?;

    println!("  ✅ Fixed file saved");
    Ok(())
}
