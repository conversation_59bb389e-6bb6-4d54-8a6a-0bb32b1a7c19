// proper_orderbook_normalization.rs
// Implement proper normalization for orderbook data based on actual distribution analysis

use polars::prelude::*;
use std::path::Path;

/// Proper normalization strategies for orderbook data
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
enum NormalizationStrategy {
    /// Rank-based normalization (converts to uniform distribution)
    RankBased,
    /// Log transformation followed by z-score (for positive values)
    LogNormal,
    /// Quantile normalization (maps to standard normal quantiles)
    QuantileNormal,
    /// Box-Cox transformation
    BoxCox(f64), // lambda parameter
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let input_files = vec![
        "/mnt/storage-box/bybit/orderbooks/output/2025-04-30_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-01_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-02_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-03_BTCUSDC_financialbert.parquet",
        "/mnt/storage-box/bybit/orderbooks/output/2025-05-04_BTCUSDC_financialbert.parquet",
    ];

    // Test different normalization strategies
    let strategies = vec![
        ("rank_based", NormalizationStrategy::RankBased),
        ("quantile_normal", NormalizationStrategy::QuantileNormal),
    ];

    for (strategy_name, strategy) in strategies {
        println!("\n🔧 Testing {} normalization strategy", strategy_name);
        
        for input_file in &input_files {
            if !Path::new(input_file).exists() {
                println!("⚠️  Skipping non-existent file: {}", input_file);
                continue;
            }

            println!("  Processing: {}", input_file);
            let output_file = input_file.replace(".parquet", &format!("_{}.parquet", strategy_name));
            apply_normalization_strategy(input_file, &output_file, &strategy)?;
        }
    }

    println!("\n✅ All normalization strategies applied!");
    Ok(())
}

fn apply_normalization_strategy(
    input_path: &str, 
    output_path: &str, 
    strategy: &NormalizationStrategy
) -> Result<(), Box<dyn std::error::Error>> {
    
    // Load the data
    let mut df = LazyFrame::scan_parquet(input_path, Default::default())?
        .collect()?;

    // Identify quantity columns
    let column_names = df.get_column_names();
    let qty_columns: Vec<String> = column_names.iter()
        .filter(|name| name.contains("_qty_"))
        .map(|s| s.to_string())
        .collect();

    println!("    Found {} quantity columns", qty_columns.len());

    match strategy {
        NormalizationStrategy::RankBased => {
            apply_rank_based_normalization(&mut df, &qty_columns)?;
        }
        NormalizationStrategy::QuantileNormal => {
            apply_quantile_normal_normalization(&mut df, &qty_columns)?;
        }
        _ => {
            return Err("Strategy not implemented yet".into());
        }
    }

    // Save the result
    let mut file = std::fs::File::create(output_path)?;
    ParquetWriter::new(&mut file)
        .with_compression(ParquetCompression::Snappy)
        .finish(&mut df)?;

    println!("    ✅ Saved to: {}", output_path);
    Ok(())
}

/// Rank-based normalization: convert to uniform [0,1] then to standard normal
fn apply_rank_based_normalization(
    df: &mut DataFrame, 
    qty_columns: &[String]
) -> Result<(), Box<dyn std::error::Error>> {
    
    for col_name in qty_columns {
        println!("      Applying rank-based normalization to: {}", col_name);
        
        // Get the column values
        let column = df.column(col_name)?;
        let values: Vec<Option<f32>> = column.f32()?.into_iter().collect();
        
        // Create (value, original_index) pairs for non-null values
        let mut value_index_pairs: Vec<(f32, usize)> = values.iter()
            .enumerate()
            .filter_map(|(i, &opt_val)| opt_val.map(|val| (val, i)))
            .collect();
        
        // Sort by value
        value_index_pairs.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
        
        // Create normalized values
        let mut normalized_values = vec![None; values.len()];
        let n = value_index_pairs.len() as f32;
        
        for (rank, &(_, original_idx)) in value_index_pairs.iter().enumerate() {
            // Convert rank to uniform [0,1]
            let uniform = (rank as f32 + 0.5) / n;
            
            // Convert uniform to standard normal using inverse normal CDF approximation
            let normal_val = inverse_normal_cdf(uniform);
            normalized_values[original_idx] = Some(normal_val);
        }
        
        // Create new column
        let normalized_col = Float32Chunked::from_iter_options(col_name.into(), normalized_values.into_iter())
            .into_series();
        
        // Replace the column
        df.replace(col_name, normalized_col)?;
    }
    
    Ok(())
}

/// Quantile normalization: map empirical quantiles to theoretical normal quantiles
fn apply_quantile_normal_normalization(
    df: &mut DataFrame, 
    qty_columns: &[String]
) -> Result<(), Box<dyn std::error::Error>> {
    
    // Collect all quantity values to compute global quantiles
    let mut all_quantities = Vec::new();
    for col_name in qty_columns {
        let column = df.column(col_name)?;
        let values: Vec<f32> = column.f32()?
            .into_iter()
            .filter_map(|opt_val| opt_val)
            .collect();
        all_quantities.extend(values);
    }
    
    // Sort for quantile computation
    all_quantities.sort_by(|a, b| a.partial_cmp(b).unwrap());
    println!("      Computing quantiles from {} total values", all_quantities.len());
    
    // Apply quantile normalization to each column
    for col_name in qty_columns {
        println!("      Applying quantile normalization to: {}", col_name);
        
        let column = df.column(col_name)?;
        let values: Vec<Option<f32>> = column.f32()?.into_iter().collect();
        
        let normalized_values: Vec<Option<f32>> = values.into_iter()
            .map(|opt_val| {
                opt_val.map(|val| {
                    // Find quantile of this value in the global distribution
                    let quantile = find_quantile(&all_quantities, val);
                    // Map to standard normal
                    inverse_normal_cdf(quantile)
                })
            })
            .collect();
        
        // Create new column
        let normalized_col = Float32Chunked::from_iter_options(col_name.into(), normalized_values.into_iter())
            .into_series();
        
        // Replace the column
        df.replace(col_name, normalized_col)?;
    }
    
    Ok(())
}

/// Find the quantile (0-1) of a value in a sorted array
fn find_quantile(sorted_values: &[f32], target: f32) -> f32 {
    if sorted_values.is_empty() {
        return 0.5;
    }
    
    // Binary search for the position
    let pos = sorted_values.binary_search_by(|&x| x.partial_cmp(&target).unwrap())
        .unwrap_or_else(|x| x);
    
    (pos as f32 + 0.5) / sorted_values.len() as f32
}

/// Approximate inverse normal CDF (Beasley-Springer-Moro algorithm)
fn inverse_normal_cdf(p: f32) -> f32 {
    if p <= 0.0 { return -6.0; }
    if p >= 1.0 { return 6.0; }
    
    // Beasley-Springer-Moro approximation
    let a = [
        -3.969683028665376e+01,
         2.209460984245205e+02,
        -2.759285104469687e+02,
         1.383577518672690e+02,
        -3.066479806614716e+01,
         2.506628277459239e+00
    ];
    
    let b = [
        -5.447609879822406e+01,
         1.615858368580409e+02,
        -1.556989798598866e+02,
         6.680131188771972e+01,
        -1.328068155288572e+01
    ];
    
    let c = [
        -7.784894002430293e-03,
        -3.223964580411365e-01,
        -2.400758277161838e+00,
        -2.549732539343734e+00,
         4.374664141464968e+00,
         2.938163982698783e+00
    ];
    
    let d = [
         7.784695709041462e-03,
         3.224671290700398e-01,
         2.445134137142996e+00,
         3.754408661907416e+00
    ];
    
    let p_low = 0.02425;
    let p_high = 1.0 - p_low;
    
    if p < p_low {
        // Rational approximation for lower region
        let q = (-2.0 * p.ln()).sqrt();
        return (((((c[0]*q+c[1])*q+c[2])*q+c[3])*q+c[4])*q+c[5]) /
               ((((d[0]*q+d[1])*q+d[2])*q+d[3])*q+1.0);
    } else if p <= p_high {
        // Rational approximation for central region
        let q = p - 0.5;
        let r = q * q;
        return (((((a[0]*r+a[1])*r+a[2])*r+a[3])*r+a[4])*r+a[5])*q /
               (((((b[0]*r+b[1])*r+b[2])*r+b[3])*r+b[4])*r+1.0);
    } else {
        // Rational approximation for upper region
        let q = (-2.0 * (1.0 - p).ln()).sqrt();
        return -(((((c[0]*q+c[1])*q+c[2])*q+c[3])*q+c[4])*q+c[5]) /
                ((((d[0]*q+d[1])*q+d[2])*q+d[3])*q+1.0);
    }
}
